import { Injectable, Logger } from '@nestjs/common';
import { 
  ILlmProvider, 
  ILlmProviderFactory, 
  LlmProviderConfig, 
  LlmProviderType 
} from '../interfaces/llm-provider.interface';
import { OpenAIProvider } from './openai.provider';
import { GoogleProvider } from './google.provider';

@Injectable()
export class LlmProviderFactory implements ILlmProviderFactory {
  private readonly logger = new Logger(LlmProviderFactory.name);

  createProvider(type: LlmProviderType, config: LlmProviderConfig): ILlmProvider {
    this.logger.debug(`Creating provider of type: ${type}`);

    switch (type) {
      case LlmProviderType.OPENAI:
        return new OpenAIProvider(config);
      
      case LlmProviderType.GOOGLE:
        return new GoogleProvider(config);
      
      case LlmProviderType.AZURE_OPENAI:
        // For Azure OpenAI, we can reuse the OpenAI provider with different base URL
        return new OpenAIProvider({
          ...config,
          baseUrl: config.baseUrl || 'https://your-resource.openai.azure.com/openai/deployments/your-deployment',
        });
      
      case LlmProviderType.ANTHROPIC:
        // Placeholder for future Anthropic implementation
        throw new Error('Anthropic provider not yet implemented');
      
      default:
        throw new Error(`Unsupported provider type: ${type}`);
    }
  }

  getSupportedProviders(): LlmProviderType[] {
    return [
      LlmProviderType.OPENAI,
      LlmProviderType.GOOGLE,
      LlmProviderType.AZURE_OPENAI,
      // LlmProviderType.ANTHROPIC, // Uncomment when implemented
    ];
  }
}
