import { Injectable, Logger } from '@nestjs/common';
import { GoogleGenerativeAI, GenerativeModel, Part } from '@google/generative-ai';
import { ChatMessage, Tool, ToolCall } from '@otrs-ai-powered/shared';
import { ILlmProvider, LlmProviderConfig, LlmCompletionResponse } from '../interfaces/llm-provider.interface';

@Injectable()
export class GoogleProvider implements ILlmProvider {
  private readonly logger = new Logger(GoogleProvider.name);
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;
  private config: LlmProviderConfig;

  constructor(config: LlmProviderConfig) {
    this.config = config;
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: config.model,
      generationConfig: {
        temperature: config.temperature || 0.7,
        maxOutputTokens: config.maxTokens || 2000,
      },
    });
  }

  async generateCompletion(
    messages: ChatMessage[],
    tools?: Tool[],
  ): Promise<LlmCompletionResponse> {
    try {
      this.logger.debug(`Generating completion with ${messages.length} messages`);

      // Convert messages to Gemini format
      const geminiMessages = this.convertMessagesToGeminiFormat(messages);

      // Create the chat session
      const chat = this.model.startChat({
        history: geminiMessages.slice(0, -1), // All but the last message
      });

      // Get the last message content
      const lastMessage = geminiMessages[geminiMessages.length - 1];
      const prompt = this.extractTextFromParts(lastMessage.parts);

      // Check if this looks like a ticket creation request
      if (this.shouldCreateTicket(prompt)) {
        return this.generateTicketCreationResponse(prompt);
      }

      // Generate response
      const result = await chat.sendMessage(prompt);
      const response = await result.response;
      const text = response.text();

      this.logger.debug(`Generated response: ${text.substring(0, 100)}...`);

      return {
        content: text,
        usage: {
          promptTokens: 0, // Gemini doesn't provide detailed token usage in free tier
          completionTokens: 0,
          totalTokens: 0,
        },
      };
    } catch (error) {
      this.logger.error('Error generating completion:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Google Gemini API error: ${errorMessage}`);
    }
  }

  private convertMessagesToGeminiFormat(messages: ChatMessage[]): any[] {
    return messages
      .filter(msg => msg.role !== 'system') // Gemini doesn't have system messages
      .map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }],
      }));
  }

  private extractTextFromParts(parts: Part[]): string {
    return parts
      .filter(part => 'text' in part)
      .map(part => part.text)
      .join(' ');
  }

  private shouldCreateTicket(prompt: string): boolean {
    const lowerPrompt = prompt.toLowerCase();
    return (
      lowerPrompt.includes('create ticket') ||
      lowerPrompt.includes('new ticket') ||
      lowerPrompt.includes('open ticket') ||
      lowerPrompt.includes('submit ticket') ||
      lowerPrompt.includes('ticket for') ||
      lowerPrompt.includes('need help with') ||
      lowerPrompt.includes('having issues') ||
      lowerPrompt.includes('problem with') ||
      lowerPrompt.includes('network issue') ||
      lowerPrompt.includes('server down') ||
      lowerPrompt.includes('can\'t access') ||
      lowerPrompt.includes('not working')
    );
  }

  private generateTicketCreationResponse(prompt: string): LlmCompletionResponse {
    // Extract ticket details from the prompt
    const ticketDetails = this.extractTicketDetails(prompt);

    return {
      content: `I'll help you create a ticket for this issue. Let me process that for you.`,
      toolCalls: [
        {
          toolId: `ticket_${Date.now()}`,
          toolName: 'create_ticket',
          arguments: ticketDetails,
        },
      ],
    };
  }

  private extractTicketDetails(prompt: string): Record<string, any> {
    const lowerPrompt = prompt.toLowerCase();

    // Determine priority based on keywords
    let priority = 'medium';
    if (lowerPrompt.includes('urgent') || lowerPrompt.includes('critical') || lowerPrompt.includes('down')) {
      priority = 'high';
    } else if (lowerPrompt.includes('minor') || lowerPrompt.includes('small')) {
      priority = 'low';
    }

    // Determine queue based on keywords
    let queue = 'General';
    if (lowerPrompt.includes('network') || lowerPrompt.includes('internet') || lowerPrompt.includes('connection')) {
      queue = 'Network';
    } else if (lowerPrompt.includes('server') || lowerPrompt.includes('database') || lowerPrompt.includes('application')) {
      queue = 'IT Support';
    } else if (lowerPrompt.includes('email') || lowerPrompt.includes('outlook') || lowerPrompt.includes('mail')) {
      queue = 'Email Support';
    }

    // Generate title and description
    const title = this.generateTicketTitle(prompt);
    const description = `User reported: ${prompt}\n\nThis ticket was automatically created by the AI assistant.`;

    return {
      title,
      description,
      priority,
      queue,
      customer: '<EMAIL>', // In real implementation, get from user context
      tags: ['ai-generated', 'auto-created'],
    };
  }

  private generateTicketTitle(prompt: string): string {
    // Simple title generation based on common patterns
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('network')) {
      return 'Network connectivity issue';
    } else if (lowerPrompt.includes('server')) {
      return 'Server access problem';
    } else if (lowerPrompt.includes('email')) {
      return 'Email system issue';
    } else if (lowerPrompt.includes('login') || lowerPrompt.includes('access')) {
      return 'Login/Access issue';
    } else if (lowerPrompt.includes('slow') || lowerPrompt.includes('performance')) {
      return 'Performance issue';
    } else {
      return 'General support request';
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      if (!this.config.apiKey) {
        this.logger.error('Google API key is missing');
        return false;
      }

      // Test the API with a simple request
      const testResult = await this.model.generateContent('Hello');
      const response = testResult.response;
      const text = response.text();

      this.logger.debug('Google Gemini API validation successful');
      return !!text;
    } catch (error) {
      this.logger.error('Google Gemini API validation failed:', error);
      return false;
    }
  }

  getProviderName(): string {
    return 'google';
  }

  getModel(): string {
    return this.config.model;
  }
}
